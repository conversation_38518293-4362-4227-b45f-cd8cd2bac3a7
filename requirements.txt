aiohttp>=3.10.11
aiosignal==1.3.1
alembic==1.13.1
amqp==5.2.0
annotated-types==0.7.0
anyio==4.4.0
APScheduler==3.10.4
asgiref==3.8.1
attrs==23.2.0
Authlib==1.3.1
azure-core==1.30.2
azure-identity==1.17.1
azure-keyvault-secrets==4.8.0
backoff==2.2.1
bcrypt==4.1.3
beautifulsoup4==4.12.3
billiard==4.2.0
bleach==6.1.0
blinker==1.8.2
build==1.2.2.post1
cachelib==0.13.0
cachetools==5.5.0
celery==5.4.0
certifi>=2024.7.4
cffi==1.16.0
charset-normalizer==3.3.2
chroma-hnswlib==0.7.6
chromadb==0.6.2
click==8.1.7
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
coloredlogs==15.0.1
cryptography>=44.0.1
dataclasses-json==0.6.7
defusedxml==0.7.1
Deprecated==1.2.14
dirtyjson==1.0.8
distro==1.9.0
dnspython==2.6.1
durationpy==0.9
email_validator==2.2.0
fastapi==0.115.6
feedparser==6.0.11
filelock==3.16.1
Flask==3.0.3
Flask-APScheduler==1.13.1
Flask-Bcrypt==1.0.1
Flask-Cors>=6.0.0
Flask-Limiter==3.8.0
Flask-Mail==0.10.0
Flask-Migrate==4.0.7
Flask-Principal==0.4.0
Flask-Script==2.0.6
Flask-Session==0.8.0

Flask-SQLAlchemy==3.1.1
Flask-WTF==1.2.1
flatbuffers==24.12.23
frozenlist==1.4.1
fsspec==2024.10.0
geoip2==4.8.0
gevent==24.11.1
google-auth==2.37.0
googleapis-common-protos==1.66.0
greenlet==3.1.1
greenletio==0.11.0
grpcio==1.69.0
gunicorn>=23.0.0
h11>=0.16.0
httpcore==1.0.5
httptools==0.6.4
httpx==0.27.0
huggingface-hub==0.27.1
humanfriendly==10.0
idna==3.7
importlib_metadata==8.5.0
importlib_resources==6.4.0
ipwhois==1.3.0
isodate==0.6.1
itsdangerous==2.2.0
Jinja2>=3.1.6
jiter==0.6.1
joblib==1.4.2
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.22.0
jsonschema-specifications==2023.12.1
kombu==5.3.7
kubernetes==31.0.0
langchain==0.3.4
langchain-core>=0.3.15
langchain-openai==0.2.3
langchain-text-splitters==0.3.0
langsmith==0.1.137
limits==3.13.0
llama-cloud==0.1.4
llama-index>=0.11.0
llama-index-agent-openai>=0.3.0
llama-index-cli>=0.3.0
llama-index-core>=0.11.0
llama-index-embeddings-openai>=0.2.0
llama-index-indices-managed-llama-cloud>=0.3.0
llama-index-legacy>=0.10.0
llama-index-llms-ollama>=0.3.0
llama-index-llms-openai>=0.2.0
llama-index-multi-modal-llms-openai>=0.2.0
llama-index-program-openai>=0.2.0
llama-index-question-gen-openai>=0.2.0
llama-index-readers-file>=0.2.0
llama-index-readers-llama-parse>=0.2.0
llama-parse==0.5.12
lmdb==1.6.2
logzero==1.7.0
Mako==1.3.5
Markdown==3.7
markdown-it-py==3.0.0
markdown2==2.5.0
MarkupSafe==2.1.5
marshmallow==3.23.0
maxminddb==2.6.2
mdurl==0.1.2
mistune==3.0.2
mmh3==5.0.1
monotonic==1.6
mpmath==1.3.0
msal==1.31.0
msal-extensions==1.2.0
msgspec==0.18.6
multidict==6.0.5
mypy-extensions==1.0.0
nest-asyncio==1.6.0
networkx==3.4.2
nltk==3.9.1
numpy==1.26.4
oauthlib==3.2.2
ollama==0.3.3
onnxruntime==1.20.1
openai==1.52.1
opentelemetry-api==1.29.0
opentelemetry-exporter-otlp-proto-common==1.29.0
opentelemetry-exporter-otlp-proto-grpc==1.29.0
opentelemetry-instrumentation==0.50b0
opentelemetry-instrumentation-asgi==0.50b0
opentelemetry-instrumentation-fastapi==0.50b0
opentelemetry-proto==1.29.0
opentelemetry-sdk==1.29.0
opentelemetry-semantic-conventions==0.50b0
opentelemetry-util-http==0.50b0
ordered-set==4.1.0
orjson==3.10.10
overrides==7.7.0
packaging==24.1
pandas==2.2.3
passlib==1.7.4
pillow==11.0.0
portalocker==2.10.1
posthog==3.7.5
prompt_toolkit==3.0.47
protobuf==5.29.2
psycopg2-binary==2.9.9
pyasn1==0.6.1
pyasn1_modules==0.4.1
pycparser==2.22
pydantic==2.8.2
pydantic_core==2.20.1
Pygments==2.18.0
PyJWT==2.9.0
pyotp==2.9.0
pypdf==4.3.1
PyPika==0.48.9
pyproject_hooks==1.2.0
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
pytz==2024.1
PyYAML==6.0.2
qrcode==8.0
redis==5.0.8
referencing==0.35.1
regex==2024.9.11
requests==2.32.3
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
rich==13.7.1
rpds-py==0.18.1
rsa==4.9
setuptools>=78.1.1
sgmllib3k==1.0.0
shellingham==1.5.4
six==1.16.0
sniffio==1.3.1
soupsieve==2.6
SQLAlchemy==2.0.31
starlette==0.41.3
stripe==10.10.0
striprtf==0.0.26
sympy==1.13.3
tenacity==8.5.0
tiktoken==0.8.0
tokenizers==0.21.0
tqdm==4.66.4
typer==0.15.1
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2024.1
tzlocal==5.2
urllib3==2.2.2
uvicorn==0.34.0
uvloop==0.21.0
vine==5.1.0
vulture==2.14
watchfiles==1.0.3
wcwidth==0.2.13
weasyprint>=65.1
webencodings==0.5.1

Werkzeug>=3.0.6
whois==1.20240129.2
wrapt==1.16.0
WTForms==3.1.2
yarl==1.9.4
zipp==3.21.0
zope.event==5.0
zope.interface==7.2
