# Filepath: gunicorn.conf.py
import multiprocessing
import os

# Basic config
bind = "unix:/home/<USER>/wegweiser/wegweiser.sock"
workers = 4  # Restored to original multi-worker configuration
worker_class = "sync"  # Restored to original sync worker
timeout = 120
graceful_timeout = 30  # Time to finish processing requests during restart
keepalive = 5  # How long to wait for requests on a Keep-Alive connection
max_requests = 1000  # Restart workers after handling this many requests
max_requests_jitter = 200  # Add randomness to max_requests to avoid all workers restarting at once
umask = 7

# Logging
accesslog = "/var/log/wegweiser/gunicorn_access.log"
errorlog = "/var/log/wegweiser/gunicorn_error.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# Process naming
proc_name = "wegweiser_app"

# Security
limit_request_line = 4094
limit_request_fields = 100
limit_request_field_size = 8190